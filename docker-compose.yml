version: "3.8"
services:
  db:
    image: postgres:15
    environment:
      POSTGRES_USER: campgrid
      POSTGRES_PASSWORD: password
      POSTGRES_DB: campgrid
    volumes:
      - db_data:/var/lib/postgresql/data
    networks:
      - campgrid

  api:
    build: ./backend
    volumes:
      - ./backend:/app
    ports:
      - "8000:8000"
    depends_on:
      db:
        condition: service_started
    networks:
      - campgrid
    environment:
      - DATABASE_URL=**************************************/campgrid

  frontend:
    build: ./frontend
    volumes:
      - ./frontend:/app
    ports:
      - "5173:5173"
    networks:
      - campgrid

volumes:
  db_data:

networks:
  campgrid: