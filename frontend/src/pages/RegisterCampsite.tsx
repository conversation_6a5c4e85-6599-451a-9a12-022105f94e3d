import React, { useState } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';

const RegisterCampsite = () => {
    const [name, setName] = useState('');
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const navigate = useNavigate();

    const handleRegister = async () => {
        try {
            const response = await axios.post('/api/campsites/register', {
                name,
                email,
                password,
            });
            alert(response.data.message);
            navigate('/');
        } catch (error) {
            console.error('Error registering campsite:', error);
            alert('Failed to register campsite.' + error);
        }
    };

    return (
        <div>
            <h1>Register Campsite</h1>
            <input
                type="text"
                placeholder="Campsite Name"
                value={name}
                onChange={(e) => setName(e.target.value)}
            />
            <br />
            <input
                type="email"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
            />
            <br />
            <input
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
            />
            <br />
            <button onClick={handleRegister}>Register</button>
        </div>
    );
};

export default RegisterCampsite;
