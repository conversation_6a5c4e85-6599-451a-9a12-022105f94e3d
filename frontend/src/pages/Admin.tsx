import React, { useEffect, useState } from 'react';
import axios from 'axios';

interface User {
    email: string;
    username: string;
    role: string;
}

const AdminDashboard = () => {
    const [users, setUsers] = useState<User[]>([]);

    useEffect(() => {
        // Fetch users from the backend
        axios.get<User[]>('/api/users')
            .then((response: { data: User[] }) => {
                console.log('Fetched users:', response.data); // Debugging log
                setUsers(response.data);
            })
            .catch((error: any) => {
                console.error('Error fetching users:', error);
            });
    }, []);

    return (
        <div>
            <h1>Admin Dashboard</h1>
            <h2>List of Users</h2>
            <ul>
                {users.map(user => (
                    <li key={user.email}>{user.username} ({user.email}) - Role: {user.role}</li>
                ))}
            </ul>
        </div>
    );
};

export default AdminDashboard;