"""Initial migration

Revision ID: 9d5c4853846d
Revises: 
Create Date: 2025-06-22 18:39:34.350780

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9d5c4853846d'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Create users table
    op.create_table('users',
        sa.Column('email', sa.String(), nullable=False),
        sa.Column('username', sa.String(), nullable=False),
        sa.Column('password_hash', sa.String(), nullable=False),
        sa.Column('role', sa.String(), nullable=False),
        sa.PrimaryKeyConstraint('email'),
        sa.UniqueConstraint('email'),
        sa.UniqueConstraint('username')
    )

    # Create campsite table
    op.create_table('campsite',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('operator_email', sa.String(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('approved', sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(['operator_email'], ['users.email'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('id')
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table('campsite')
    op.drop_table('users')
