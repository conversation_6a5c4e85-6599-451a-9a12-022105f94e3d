<!DOCTYPE html>
<html>
<head>
    <title>Test Frontend</title>
</head>
<body>
    <h1>Test CampGrid API</h1>
    
    <div>
        <h2>Login</h2>
        <input type="text" id="username" placeholder="Username" value="<EMAIL>">
        <input type="password" id="password" placeholder="Password" value="admin">
        <button onclick="login()">Login</button>
    </div>
    
    <div>
        <h2>Get Users (Admin only)</h2>
        <button onclick="getUsers()">Get Users</button>
        <div id="users"></div>
    </div>
    
    <script>
        let token = null;
        
        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('http://localhost:8000/token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                console.log('Login response:', data);
                
                if (data.access_token) {
                    token = data.access_token;
                    localStorage.setItem('token', token);
                    alert('Login successful!');
                } else {
                    alert('Login failed!');
                }
            } catch (error) {
                console.error('Login error:', error);
                alert('Login error: ' + error.message);
            }
        }
        
        async function getUsers() {
            const storedToken = localStorage.getItem('token') || token;
            
            if (!storedToken) {
                alert('Please login first!');
                return;
            }
            
            try {
                console.log('Using token:', storedToken);
                
                const response = await fetch('http://localhost:8000/api/users', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${storedToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                if (response.ok) {
                    const users = await response.json();
                    console.log('Users:', users);
                    document.getElementById('users').innerHTML = '<pre>' + JSON.stringify(users, null, 2) + '</pre>';
                } else {
                    const error = await response.text();
                    console.error('Error response:', error);
                    alert('Error: ' + error);
                }
            } catch (error) {
                console.error('Get users error:', error);
                alert('Get users error: ' + error.message);
            }
        }
        
        // Load token from localStorage on page load
        window.onload = function() {
            token = localStorage.getItem('token');
            if (token) {
                console.log('Token loaded from localStorage:', token);
            }
        };
    </script>
</body>
</html>
