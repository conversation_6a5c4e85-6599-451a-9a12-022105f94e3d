from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from starlette.status import HTTP_401_UNAUTHORIZED
import jwt
from sqlalchemy.orm import Session
from .models import SessionLocal, User
from passlib.context import CryptContext
from .api import router
from .config import oauth2_scheme, SECRET_KEY

import logging

logging.basicConfig(level=logging.DEBUG)  # Ändere zu DEBUG, INFO, WARNING, ERROR oder CRITICAL
logger = logging.getLogger(__name__)

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

pwd_context = CryptContext(schemes=["bcrypt"])

@app.post("/token")
async def login(form_data: dict):
    db: Session = SessionLocal()
    user = db.query(User).filter((User.email == form_data["username"]) | (User.username == form_data["username"])).first()
    if not user or not pwd_context.verify(form_data["password"], user.password_hash):
        raise HTTPException(status_code=HTTP_401_UNAUTHORIZED, detail="Invalid credentials")

    token = jwt.encode({"sub": user.email, "role": user.role}, SECRET_KEY, algorithm="HS256")
    return {"access_token": token, "token_type": "bearer"}

@app.get("/me")
async def read_users_me(token: str = Depends(oauth2_scheme)):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
        return {"username": payload["sub"], "role": payload["role"]}
    except:
        raise HTTPException(status_code=HTTP_401_UNAUTHORIZED, detail="Invalid token")

app.include_router(router, prefix="/api")
