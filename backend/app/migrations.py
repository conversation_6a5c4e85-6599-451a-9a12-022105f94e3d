"""
Database migration utilities for CampGrid application.
"""
import os
import sys
import logging
from alembic.config import Config
from alembic import command
from sqlalchemy import create_engine, text
from sqlalchemy.exc import OperationalError
import time

logger = logging.getLogger(__name__)

def wait_for_db(database_url: str, max_retries: int = 30, retry_interval: int = 1):
    """
    Wait for database to be available.
    
    Args:
        database_url: Database connection URL
        max_retries: Maximum number of connection attempts
        retry_interval: Time to wait between attempts in seconds
    """
    engine = create_engine(database_url)
    
    for attempt in range(max_retries):
        try:
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            logger.info("Database connection successful")
            return True
        except OperationalError as e:
            if attempt < max_retries - 1:
                logger.info(f"Database not ready (attempt {attempt + 1}/{max_retries}), waiting {retry_interval}s...")
                time.sleep(retry_interval)
            else:
                logger.error(f"Failed to connect to database after {max_retries} attempts: {e}")
                raise
    
    return False

def run_migrations(database_url: str = None):
    """
    Create database tables using SQLAlchemy metadata.

    Args:
        database_url: Database connection URL
    """
    try:
        if not database_url:
            from .models import DATABASE_URL
            database_url = DATABASE_URL

        logger.info("Creating database tables...")

        # Import models to ensure they are registered with Base
        from .models import Base, User, Campsite

        # Create engine and tables
        engine = create_engine(database_url)
        Base.metadata.create_all(bind=engine)

        logger.info("Database tables created successfully")
        return True

    except Exception as e:
        logger.error(f"Failed to create tables: {e}")
        return False

def initialize_database(database_url: str = None):
    """
    Initialize database by waiting for connection and running migrations.
    
    Args:
        database_url: Database connection URL
    """
    if not database_url:
        from .models import DATABASE_URL
        database_url = DATABASE_URL
    
    logger.info("Initializing database...")
    
    # Wait for database to be available
    if not wait_for_db(database_url):
        raise RuntimeError("Database is not available")
    
    # Run migrations
    if not run_migrations(database_url):
        raise RuntimeError("Failed to run database migrations")
    
    logger.info("Database initialization completed")

if __name__ == "__main__":
    # Allow running migrations directly
    logging.basicConfig(level=logging.INFO)
    initialize_database()
