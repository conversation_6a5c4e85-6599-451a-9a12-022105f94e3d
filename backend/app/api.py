from fastapi import APIRouter
import uuid

router = APIRouter()

@router.get("/")
def read_root():
    return {"message": "Welcome to CampGrid API"}

@router.get("/users")
def get_users():
    from .models import SessionLocal, User
    session = SessionLocal()
    try:
        users = session.query(User).all()
        return [
            {"email": user.email, "username": user.username, "role": user.role}
            for user in users
        ]
    finally:
        session.close()

@router.get("/campsites")
def get_campsites():
    from .models import SessionLocal, Campsite
    session = SessionLocal()
    try:
        campsites = session.query(Campsite).all()
        return [
            {"id": campsite.id, "operator_email": campsite.operator_email, "name": campsite.name}
            for campsite in campsites
        ]
    finally:
        session.close()

@router.post("/campsites/register")
def register_campsite(data: dict):
    from .models import SessionLocal, Campsite, User

    session = SessionLocal()
    try:
        # Validate input data
        if not data.get("name") or not data.get("email") or not data.get("password"):
            return {"message": "Missing required fields.", "status": "error"}

        # Check if campsite name is already taken
        existing_campsite = session.query(Campsite).filter_by(name=data["name"]).first()
        if existing_campsite:
            return {"message": "Campsite name already taken.", "status": "error"}

        # Check if operator user already exists
        existing_user = session.query(User).filter_by(email=data["email"]).first()
        if not existing_user:
            # Create new operator user
            from passlib.context import CryptContext
            pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
            new_user = User(
                email=data["email"],
                username=data["email"].split("@")[0],
                password_hash=pwd_context.hash(data["password"]),
                role="Operator"
            )
            session.add(new_user)
            session.commit()
            operator_email = new_user.email
        else:
            operator_email = existing_user.email

        # Create new campsite
        new_campsite = Campsite(
            id=str(uuid.uuid4()),
            operator_email=data["email"],
            name=data["name"],
            approved=False
        )
        session.add(new_campsite)
        session.commit()

        return {"message": "Campsite registered successfully. Please verify your email.", "status": "success"}
    except Exception as e:
        session.rollback()
        return {"message": f"An error occurred: {str(e)}", "status": "error"}
    finally:
        session.close()

@router.post("/users/register")
def register_user(data: dict):
    from .models import SessionLocal, User
    from passlib.context import CryptContext

    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    session = SessionLocal()
    try:
        # Validate input data
        if not data.get("email") or not data.get("username") or not data.get("password"):
            return {"message": "Missing required fields.", "status": "error"}

        # Check if user already exists
        existing_user = session.query(User).filter_by(email=data["email"]).first()
        if existing_user:
            return {"message": "User already exists.", "status": "error"}

        # Create new user
        new_user = User(
            email=data["email"],
            username=data["username"],
            password_hash=pwd_context.hash(data["password"]),
            role="User"
        )
        session.add(new_user)
        session.commit()

        return {"message": "User registered successfully.", "status": "success"}
    except Exception as e:
        session.rollback()
        return {"message": f"An error occurred: {str(e)}", "status": "error"}
    finally:
        session.close()