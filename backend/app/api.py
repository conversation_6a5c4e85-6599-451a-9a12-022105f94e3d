from fastapi import <PERSON>Rout<PERSON>, Depends, HTTPException
from starlette.status import HTTP_403_FORBIDDEN
from .config import oauth2_scheme, SECRET_KEY
import jwt
import uuid
import logging
import sys

# Logging-Konfiguration
#print(logging.root.manager.loggerDict.keys())
print("Hello World!")
logger = logging.getLogger("uvicorn")
logger.setLevel(logging.DEBUG)

router = APIRouter()

@router.get("/")
def read_root():
    return {"message": "Welcome to CampGrid API"}

@router.get("/users")
def get_users(token: str = Depends(oauth2_scheme)):
    print("get users")
    from .models import SessionLocal, User
    session = SessionLocal()
    try:
        # Debugging: Log the received token
        print(f"Received token: {token}")

        # Decode the token
        payload = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
        print(f"Token payload: {payload}")

        # Check for admin role
        if payload.get("role") != "admin":
            print(f"Role in token: {payload.get('role')}")
            raise HTTPException(status_code=HTTP_403_FORBIDDEN, detail="Access denied")

        # Fetch users from the database
        print("Fetching users from the database...")
        users = session.query(User).all()
        print(f"Fetched {len(users)} users.")
        return [
            {"email": user.email, "username": user.username, "role": user.role}
            for user in users
        ]
    except jwt.ExpiredSignatureError:
        print("Token expired")
        raise HTTPException(status_code=HTTP_403_FORBIDDEN, detail="Token expired")
    except jwt.InvalidTokenError:
        print("Invalid token")
        raise HTTPException(status_code=HTTP_403_FORBIDDEN, detail="Invalid token")
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=HTTP_403_FORBIDDEN, detail="An error occurred during token validation")
    finally:
        session.close()
        print("Database session closed.")

@router.get("/campsites")
def get_campsites():
    from .models import SessionLocal, Campsite
    session = SessionLocal()
    try:
        campsites = session.query(Campsite).all()
        return [
            {"id": campsite.id, "email": campsite.email, "name": campsite.name}
            for campsite in campsites
        ]
    finally:
        session.close()

@router.post("/campsites/register")
def register_campsite(data: dict):
    from .models import SessionLocal, Campsite, User
    from passlib.context import CryptContext

    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

    session = SessionLocal()
    try:
        # Validate input data
        if not data.get("name") or not data.get("email") or not data.get("password"):
            return {"message": "Missing required fields.", "status": "error"}

        # Check if campsite name is already taken
        existing_campsite = session.query(Campsite).filter_by(name=data["name"]).first()
        if existing_campsite:
            return {"message": "Campsite name already taken.", "status": "error"}

        # Check if operator user already exists
        existing_user = session.query(User).filter_by(email=data["email"]).first()
        if not existing_user:
            # Create new user
            new_user = User(
                email=data["email"],
                username=data["email"],
                password_hash=pwd_context.hash(data["password"]),
                role="owner"
            )
            session.add(new_user)
            session.commit()

        # Create new campsite
        new_campsite = Campsite(
            id=str(uuid.uuid4()),
            email=data["email"],
            name=data["name"],
            approved=False
        )
        session.add(new_campsite)
        session.commit()

        return {"message": "Campsite registered successfully. Please verify your email.", "status": "success"}
    except Exception as e:
        session.rollback()
        return {"message": f"An error occurred: {str(e)}", "status": "error"}
    finally:
        session.close()

@router.post("/users/register")
def register_user(data: dict):
    from .models import SessionLocal, User
    from passlib.context import CryptContext

    pwd_context = CryptContext(schemes=["bcrypt"])
    session = SessionLocal()
    try:
        # Validate input data
        if not data.get("email") or not data.get("username") or not data.get("password"):
            return {"message": "Missing required fields.", "status": "error"}

        # Check if user already exists
        existing_user = session.query(User).filter_by(email=data["email"]).first()
        if existing_user:
            return {"message": "User already exists.", "status": "error"}

        # Create new user
        new_user = User(
            email=data["email"],
            username=data["username"],
            password_hash=pwd_context.hash(data["password"]),
            role="user"
        )
        session.add(new_user)
        session.commit()

        return {"message": "User registered successfully.", "status": "success"}
    except Exception as e:
        session.rollback()
        return {"message": f"An error occurred: {str(e)}", "status": "error"}
    finally:
        session.close()