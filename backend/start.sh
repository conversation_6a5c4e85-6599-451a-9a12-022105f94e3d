#!/bin/bash

# Exit on any error
set -e

echo "Starting CampGrid API..."

# Wait for database to be ready
echo "Waiting for database..."
python -c "
import time
import psycopg2
import os

max_retries = 30
retry_interval = 1

for attempt in range(max_retries):
    try:
        conn = psycopg2.connect(
            host='db',
            database='campgrid',
            user='campgrid',
            password='password'
        )
        conn.close()
        print('Database is ready!')
        break
    except psycopg2.OperationalError as e:
        if attempt < max_retries - 1:
            print(f'Database not ready (attempt {attempt + 1}/{max_retries}), waiting {retry_interval}s...')
            time.sleep(retry_interval)
        else:
            print(f'Failed to connect to database after {max_retries} attempts')
            raise
"

# Run database migrations
echo "Running database migrations..."
cd /app
alembic upgrade head

# Initialize admin user
echo "Initializing admin user..."
python -c "
from app.models import initialize_admin_user
try:
    initialize_admin_user()
    print('Admin user initialized successfully')
except Exception as e:
    print(f'Admin user initialization failed: {e}')
"

# Start the application
echo "Starting FastAPI application..."
uvicorn app.main:app --host 0.0.0.0 --port 8000
