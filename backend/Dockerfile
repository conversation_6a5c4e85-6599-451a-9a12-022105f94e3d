FROM python:3.11-slim
WORKDIR /app

# Copy requirements and install dependencies
COPY backend/requirements.txt .
RUN pip install -r requirements.txt

# Copy application code
COPY backend/app/ ./app

# Copy alembic configuration from project root
COPY alembic.ini ./alembic.ini
COPY alembic/ ./alembic/

# Create startup script directly in container
RUN echo '#!/bin/bash\n\
set -e\n\
echo "Starting CampGrid API..."\n\
echo "Waiting for database..."\n\
python -c "\n\
import time\n\
import psycopg2\n\
import os\n\
\n\
max_retries = 30\n\
retry_interval = 1\n\
\n\
for attempt in range(max_retries):\n\
    try:\n\
        conn = psycopg2.connect(\n\
            host='\''db'\'',\n\
            database='\''campgrid'\'',\n\
            user='\''campgrid'\'',\n\
            password='\''password'\''\n\
        )\n\
        conn.close()\n\
        print('\''Database is ready!'\'')\n\
        break\n\
    except psycopg2.OperationalError as e:\n\
        if attempt < max_retries - 1:\n\
            print(f'\''Database not ready (attempt {attempt + 1}/{max_retries}), waiting {retry_interval}s...'\'')\n\
            time.sleep(retry_interval)\n\
        else:\n\
            print(f'\''Failed to connect to database after {max_retries} attempts'\'')\n\
            raise\n\
"\n\
echo "Creating database tables..."\n\
cd /app\n\
python -c "from app.models import Base, engine; Base.metadata.create_all(bind=engine); print('\''Database tables created successfully'\'')"\n\
echo "Initializing admin user..."\n\
python -c "\n\
from app.models import initialize_admin_user\n\
try:\n\
    initialize_admin_user()\n\
    print('\''Admin user initialized successfully'\'')\n\
except Exception as e:\n\
    print(f'\''Admin user initialization failed: {e}'\'')\n\
"\n\
echo "Starting FastAPI application..."\n\
uvicorn app.main:app --host 0.0.0.0 --port 8000' > /app/start.sh && chmod +x /app/start.sh

CMD ["bash", "/app/start.sh"]